import { defineStore } from "pinia";

interface RouterState {
  tag: string | null;
  search: string | null;
  categories: number[];
  location: {
    city: string | null;
    latitude: number | null;
    longitude: number | null;
  } | null;
  dateRange: string | null;
}

export const useRouterStore = defineStore("router", {
  state: (): RouterState => ({
    tag: null,
    search: null,
    categories: [],
    location: null,
    dateRange: null,
  }),

  getters: {
    hasTag: (state) => !!state.tag,
    isTrending: (state) => state.tag === "trending",
    isRecommended: (state) => state.tag === "recommended",
    isNearby: (state) => state.tag === "nearby",
    hasFilters: (state) => {
      return !!(
        state.search ||
        state.categories.length > 0 ||
        state.location ||
        state.dateRange ||
        state.tag
      );
    },
  },

  actions: {
    setTag(tag: string | null) {
      this.tag = tag;
      this.updateUrl();
    },

    setSearch(search: string | null) {
      this.search = search;
      this.updateUrl();
    },

    setCategories(categories: number[]) {
      this.categories = categories;
      this.updateUrl();
    },

    setLocation(location: RouterState["location"]) {
      this.location = location;
      this.updateUrl();
    },

    setDateRange(dateRange: string | null) {
      this.dateRange = dateRange;
      this.updateUrl();
    },

    removeTag() {
      this.tag = null;
      this.updateUrl();
    },

    removeSearch() {
      this.search = null;
      this.updateUrl();
    },

    removeCategories() {
      this.categories = [];
      this.updateUrl();
    },

    removeLocation() {
      this.location = null;
      this.updateUrl();
    },

    removeDateRange() {
      this.dateRange = null;
      this.updateUrl();
    },

    clearAllFilters() {
      this.tag = null;
      this.search = null;
      this.categories = [];
      this.location = null;
      this.dateRange = null;
      this.updateUrl();
    },

    initializeFromRoute(query: any) {
      this.tag = query.tag || null;
      this.search = query.search || null;

      if (query.category) {
        if (Array.isArray(query.category)) {
          this.categories = query.category.map(Number);
        } else {
          this.categories = [Number(query.category)];
        }
      } else {
        this.categories = [];
      }

      if (query.location) {
        let loc: any = query.location;
        if (typeof loc === "string") {
          try {
            loc = JSON.parse(loc);
          } catch {}
        }
        if (loc && typeof loc === "object") {
          this.location = {
            city: loc.city || null,
            latitude: loc.latitude ? Number(loc.latitude) : null,
            longitude: loc.longitude ? Number(loc.longitude) : null,
          };
        }
      } else {
        this.location = null;
      }

      this.dateRange = query.dateRange || null;
    },

    updateUrl() {
      const router = useRouter();
      const query: Record<string, any> = {};

      if (this.tag) query.tag = this.tag;
      if (this.search) query.search = this.search;
      if (this.categories.length > 0) query.category = this.categories;
      if (this.location) query.location = JSON.stringify(this.location);
      if (this.dateRange) query.dateRange = this.dateRange;

      router.replace({ query });
    },

    buildSearchQuery(): string {
      const params: string[] = [];

      if (this.search) params.push(`title=${encodeURIComponent(this.search)}`);
      if (this.categories.length > 0)
        params.push(
          `categories=${encodeURIComponent(JSON.stringify(this.categories))}`
        );
      if (this.dateRange)
        params.push(`dateRange=${encodeURIComponent(this.dateRange)}`);

      return params.join("&");
    },
  },
});
