<template>
  <div>
    <LandingHomeHero @onQuery="onQuery" />
    <div v-if="activeFilters.length > 0" class="w-full px-5 mt-3">
      <div class="shadow flex items-center justify-between bg-white dark:bg-zinc-800 p-4">
        <div class="flex flex-wrap items-center gap-3">
          <span class="font-medium text-gray-700 dark:text-zinc-300">Active Filters:</span>
          <div v-for="filter in activeFilters" :key="filter.key"
            class="flex items-center bg-zinc-100 dark:bg-zinc-900/20 rounded-full border border-zinc-50 dark:border-zinc-800 px-3 py-1">
            <span class="text-zinc-700 font-medium dark:text-zinc-300">{{ filter.label }}</span>
            <button
              @click="removeFilter(filter.key as 'location' | 'search' | 'categories' | 'dateRange' | 'trending' | 'recommended' | 'nearby')"
              class="ml-2 text-zinc-500 font-normal hover:text-zinc-700 dark:text-zinc-400 dark:hover:text-zinc-200 transition">
              <Icon icon="zondicons:close-solid" class="w-4 h-4" />
            </button>
          </div>
        </div>
        <button @click="clearAllFilters"
          class="text-sm font-medium text-gray-500 dark:text-zinc-400 hover:text-red-600 dark:hover:text-red-400 transition">
          Clear All
        </button>
      </div>
    </div>
    <div class="w-full mt-5 px-5" v-if="loading">
      <EventsSkeletonLoader v-if="loading" :is-grid="toggleGrid" :items-to-show="itemsPerPage" />
    </div>

    <div class="w-full mt-5 px-5" v-else>
      <div class="w-full flex justify-between">
        <h3 class="text-lg sm:text-xl font-semibold dark:text-zinc-100">
          Showing {{ events.length }} events
        </h3>
        <div class="flex items-center space-x-2">
          <EventsFilters @apply-filters="onFiltersApply" />
          <div class="relative flex">
            <div class="relative">
              <button @click="changeView('grid')" :class="[
                toggleGrid
                  ? 'bg-red-600 text-white'
                  : 'bg-gray-200 text-gray-500 dark:bg-zinc-900 dark:text-zinc-50',
                'p-1.5 shiny',
              ]">
                <Icon icon="ep:grid" class="w-6 h-6" />
              </button>
              <span v-if="toggleGrid"
                class="absolute left-1/2 -bottom-1 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-t-[6px] border-t-red-600"></span>
            </div>

            <div class="relative">
              <button @click="changeView('list')" :class="[
                toggleGrid
                  ? 'bg-gray-200 text-gray-500 dark:bg-zinc-900 dark:text-zinc-50'
                  : 'bg-red-600 text-white',
                'p-1.5 shiny',
              ]">
                <Icon icon="ph:list-bold" class="w-6 h-6" />
              </button>
              <span v-if="!toggleGrid"
                class="absolute left-1/2 -bottom-1 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-t-[6px] border-t-red-600"></span>
            </div>
          </div>
        </div>
      </div>

      <div>
        <div v-if="toggleGrid" class="w-full sm:grid sm:grid-cols-3 gap-4 mt-1 space-y-4 sm:space-y-0">
          <EventsCard v-for="event in events" :key="event.id" :event="event" />
        </div>

        <div class="mt-1 w-full flex flex-col space-y-2.5" v-else>
          <EventsListview v-for="event in events" :key="event.id" :event="event" />
        </div>
      </div>

      <div class="flex flex-col items-center mt-4 dark:text-zinc-100" v-if="totalEvents > 0">
        <span class="">
          Showing
          <span class="font-semibold">{{ startIndex + 1 }}</span>
          to
          <span class="font-semibold">{{ endIndex }}</span>
          of
          <span class="font-semibold">{{ totalEvents }}</span>
          Entries
        </span>
        <div class="inline-flex mt-2 xs:mt-0">
          <button @click="prevPage" :disabled="currentPage === 1"
            class="flex items-center dark:text-zinc-100 justify-center px-4 h-10 text-base font-medium bg-gray-200 dark:bg-zinc-900 hover:bg-gray-300 transition duration-150 disabled:opacity-50 disabled:cursor-not-allowed">
            <Icon icon="grommet-icons:link-previous" class="w-5 h-5 mr-2" />
            Prev
          </button>
          <button @click="nextPage" :disabled="currentPage === totalPages"
            class="flex items-center justify-center px-4 h-10 text-base font-medium transition duration-150 text-white bg-red-600 border-0 hover:bg-red-700 disabled:cursor-not-allowed">
            Next
            <Icon icon="streamline:next" class="w-5 h-5 ml-2" />
          </button>
        </div>
      </div>

      <div v-if="totalEvents == 0" class="w-full flex flex-col space-y-2.5 items-center justify-center py-20">
        <img src="@/assets/illustrations/lighthouse.svg" class="w-auto h-72" alt="no-events-story-illustration" />
        <h3 class="font-semibold text-lg">Could not load events, please try again later...</h3>
        <button @click="fetchEvents"
          class="inline-flex items-center text-sky-500 transition-all hover:text-sky-400 duration-150">
          <Icon icon="system-uicons:refresh" class="w-5 h-5 mr-2" /> Refresh
        </button>
      </div>
    </div>

    <div class="mt-10">
      <div
        class="flex items-center max-md:flex-col gap-6 bg-gradient-to-tr from-red-700 to-pink-500 text-white px-6 py-3.5">
        <p class="text-base flex-1 max-md:text-center">
          <span class="text-xl font-semibold flex items-center">
            <Icon icon="mage:megaphone-a" class="h-6 w-6 mr-2" /> Did you know?
          </span>
          You can host your own virtual and venue events here on EventaHub? Sign up
          today and start hosting your own events.
        </p>

        <div>
          <button @click="router.push('/become-host')" type="button" class="bg-white text-black py-2.5 px-5">
            Become Host
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Category, EventItem } from '@/types';
import type { EventsResponse } from '@/types/api';
import dayjs from 'dayjs';
import type { FilterComponents } from '@/types/filters';
import { useRouterStore } from '@/store/router';

definePageMeta({
  layout: "default",
});

useHead({
  title: "Discover Events in Malawi - Browse Conferences, Concerts & More | EventaHub",
  meta: [
    {
      name: "description",
      content: "Explore and discover amazing events happening in Malawi. Browse conferences, concerts, workshops, seminars, and cultural events. Find your next experience on EventaHub Malawi.",
    },
    {
      name: 'keywords',
      content: 'events Malawi, browse events, conferences Malawi, concerts, workshops, seminars, cultural events, event discovery'
    },
    {
      property: 'og:title',
      content: 'Discover Events in Malawi - Browse Conferences, Concerts & More | EventaHub'
    },
    {
      property: 'og:description',
      content: 'Explore and discover amazing events happening in Malawi. Browse conferences, concerts, workshops, and cultural events.'
    },
    {
      property: 'og:type',
      content: 'website'
    },
    {
      name: 'twitter:card',
      content: 'summary_large_image'
    },
    {
      name: 'twitter:title',
      content: 'Discover Events in Malawi - Browse Conferences, Concerts & More'
    },
    {
      name: 'twitter:description',
      content: 'Explore and discover amazing events happening in Malawi. Find your next experience on EventaHub.'
    },
    {
      name: 'robots',
      content: 'index, follow'
    }
  ],
});

const httpClient = useHttpClient();
const toggleGrid = ref<boolean>(true);
const currentPage = ref<number>(1);
const itemsPerPage = ref<number>(6);
const totalEvents = ref<number>(0);
const events = ref<EventItem[]>([]);
const loading = ref<boolean>(false);
const router = useRouter();
const route = useRoute();
const { $toast }: any = useNuxtApp();
const routerStore = useRouterStore();

const totalPages = computed(() =>
  Math.ceil(totalEvents.value / itemsPerPage.value)
);

const startIndex = computed(() =>
  (currentPage.value - 1) * itemsPerPage.value
);

const endIndex = computed(() =>
  Math.min(startIndex.value + itemsPerPage.value, totalEvents.value)
);

const prevPage = (): void => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const nextPage = (): void => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

const changeView = (type: "grid" | "list"): void => {
  toggleGrid.value = type === "grid";
  const query = { ...route.query, view: type };
  router.replace({ query });
}

interface ActiveFilter {
  key: string;
  label: string;
}

const activeFilters = ref<ActiveFilter[]>([]);

const filterResetActions = {
  search: () => {
    routerStore.removeSearch();
    activeFilters.value = activeFilters.value.filter(filter => filter.key !== "search");
  },
  categories: () => {
    routerStore.removeCategories();
    activeFilters.value = activeFilters.value.filter(filter => filter.key !== "categories");
  },
  dateRange: () => {
    routerStore.removeDateRange();
    activeFilters.value = activeFilters.value.filter(filter => filter.key !== "dateRange");
  },
  location: () => {
    routerStore.removeLocation();
    activeFilters.value = activeFilters.value.filter(filter => filter.key !== "location");
  },
  trending: () => {
    routerStore.removeTag();
    activeFilters.value = activeFilters.value.filter(filter => filter.key !== "trending");
  },
  recommended: () => {
    routerStore.removeTag();
    activeFilters.value = activeFilters.value.filter(filter => filter.key !== "recommended");
  },
  nearby: () => {
    routerStore.removeTag();
    activeFilters.value = activeFilters.value.filter(filter => filter.key !== "nearby");
  }
};

const removeFilter = (key: keyof typeof filterResetActions) => {
  const resetAction = filterResetActions[key];
  if (resetAction) {
    resetAction();
  }
};

const clearAllFilters = () => {
  routerStore.clearAllFilters();
  activeFilters.value = [];
};

const updateActiveFilters = () => {
  activeFilters.value = [];

  if (routerStore.search) {
    activeFilters.value.push({
      key: 'search',
      label: `Search: ${routerStore.search}`
    });
  }

  if (routerStore.categories.length > 0) {
    activeFilters.value.push({
      key: 'categories',
      label: `Categories: ${routerStore.categories.length} selected`
    });
  }

  if (routerStore.dateRange) {
    const [start, end] = routerStore.dateRange.split(',');
    activeFilters.value.push({
      key: 'dateRange',
      label: `Date: ${dayjs(start).format('MMM D')} - ${dayjs(end).format('MMM D')}`
    });
  }

  if (routerStore.location?.city) {
    activeFilters.value.push({
      key: 'location',
      label: `Location: ${routerStore.location.city}`
    });
  }

  if (routerStore.tag) {
    activeFilters.value.push({
      key: routerStore.tag,
      label: routerStore.tag.charAt(0).toUpperCase() + routerStore.tag.slice(1)
    });
  }
};

const onFiltersApply = (components: FilterComponents): void => {
  if (components) {
    if (components.dateRange && components.dateRange.length === 2) {
      const dateRangeValue = `${dayjs(components.dateRange[0]).format(DATE_FORMAT.FULL)},${dayjs(components.dateRange[1]).format(DATE_FORMAT.FULL)}`;
      routerStore.setDateRange(dateRangeValue);
    }

    if (components.categories && components.categories.length > 0) {
      const categoryIds = components.categories.map((category: Category) => category.id);
      routerStore.setCategories(categoryIds);
    }

    if (components.location) {
      const locationData = {
        city: components.location.address || null,
        latitude: components.location.center?.lat ? Number(components.location.center.lat) : null,
        longitude: components.location.center?.lng ? Number(components.location.center.lng) : null
      };
      routerStore.setLocation(locationData);
    }

    updateActiveFilters();
  }
};

const onQuery = async(items: { search: string; category: string | number; location: any; }): Promise<void> => {
  routerStore.setSearch(items.search);

  if (items.category && typeof items.category === 'number') {
    routerStore.setCategories([items.category]);
  } else {
    routerStore.setCategories([]);
  }

  if(items.location){
    const locationData = {
      city: items.location.city,
      latitude: items.location.latitude,
      longitude: items.location.longitude
    };
    routerStore.setLocation(locationData);

    const isNearbyAlreadyActive = activeFilters.value.some(
      filter => filter.key === "location"
    );
    if (!isNearbyAlreadyActive) {
      activeFilters.value.push({
        key: "location",
        label: `Nearby: ${items.location.city || 'Current Location'}`,
      });
    }
  }
};

const handleTagQuery = (tag: string): void => {
  const normalizedTag = tag.toLowerCase();
  activeFilters.value = activeFilters.value.filter(filter =>
    !['trending', 'recommended', 'nearby'].includes(filter.key)
  );

  if (normalizedTag === "trending") {
    fetchTrendingEvents();
  } else if (normalizedTag === "recommended") {
    fetchRecommendedEvents();
  } else if (normalizedTag === "nearby me" || normalizedTag === "nearby") {
    $toast?.info("Please enable location to see nearby events");
    return;
  }

  activeFilters.value.push({
    key: normalizedTag,
    label: tag.charAt(0).toUpperCase() + tag.slice(1),
  });
}



const fetchEvents = async (): Promise<void> => {
  try {
    loading.value = true;
    const response = await httpClient.get<EventsResponse>(
      `${ENDPOINTS.EVENTS.GET}?per_page=${itemsPerPage.value}&page=${currentPage.value}`
    );
    if (response) {
      events.value = response.events.data;
      totalEvents.value = response.events.total;
    }
  } catch (error) {
    $toast.error('An error occurred while fetching events, please try again later.');
    console.error('Error fetching events:', error);
  } finally {
    loading.value = false;
  }
};

const searchEvents = async (query: string): Promise<void> => {
  try {
    loading.value = true;
    const response = await httpClient.get<EventsResponse>(
      `${ENDPOINTS.EVENTS.SEARCH}?per_page=${itemsPerPage.value}&page=${currentPage.value}&${query}`
    );
    if (response) {
      events.value = response.events.data;
      totalEvents.value = response.events.total;
      response.events.data.length == 0 && $toast.warn('No events found with the specified filters, please try again.');
    }
  } catch (error) {
    $toast.error('An error occurred while fetching events, please try again later.');
    console.error('Error fetching events:', error);
  } finally {
    loading.value = false;
  }
};

const fetchTrendingEvents = async (): Promise<void> => {
  try {
    loading.value = true;
    const response = await httpClient.get<EventsResponse>(
      `${ENDPOINTS.EVENTS.TRENDING}?per_page=${itemsPerPage.value}&page=${currentPage.value}`
    );
    if (response) {
      events.value = response.events.data;
      totalEvents.value = response.events.total;
    }
  } catch (error) {
    $toast.error('An error occurred while fetching events, please try again later.');
    console.error('Error fetching events:', error);
  } finally {
    loading.value = false;
  }
}

const fetchRecommendedEvents = async (): Promise<void> => {
  try {
    loading.value = true;

    const response = await httpClient.get<any>(
      `${ENDPOINTS.EVENTS.RECOMMENDED}?per_page=${itemsPerPage.value}&page=${currentPage.value}`
    );
    if (response) {
      events.value = response.events.data;
      totalEvents.value = response.events.total;
      if ((response as any).message) {
        $toast.info((response as any).message);
      }
    }
  } catch (error: any) {
    console.log(error)
  } finally {
    loading.value = false;
  }
}

const fetchNearbyEvents = async (): Promise<void> => {
  try {
    $toast.success("Searching events 100KM around you...");
    loading.value = true;
    const location = routerStore.location;
    const response = await httpClient.post<EventsResponse>(
      `${ENDPOINTS.EVENTS.NEARBY}?per_page=${itemsPerPage.value}&page=${currentPage.value}&latitude=${location?.latitude || 0}&longitude=${location?.longitude || 0}&radius=10990&city=${location?.city || ''}`
    );
    if (response) {
      events.value = response.events.data;
      totalEvents.value = response.events.total;
    }
  } catch (error) {
    $toast.error('An error occurred while fetching events, please try again later.');
    console.error('Error fetching events:', error);
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  const params = router.currentRoute.value.query;
  routerStore.initializeFromRoute(params);

  let hasSpecialQuery = false;

  if (params.search || params.category || params.location || params.tag) {
    hasSpecialQuery = true;
    updateActiveFilters();
  }

  if (params.tag) {
    const tag = params.tag as string;
    handleTagQuery(tag);
  } else if (!hasSpecialQuery) {
    fetchEvents();
  }
})

watch(currentPage, (newPage: number, oldPage: number) => {
  if (newPage !== oldPage) {
    fetchEvents();
  }
});

watch(
  [() => routerStore.search, () => routerStore.categories, () => routerStore.dateRange, () => routerStore.location],
  ([newQuery, newCategories, newDateRange, newLocation]) => {
    if (newQuery || newCategories.length > 0 || newDateRange) {
      const searchQuery = routerStore.buildSearchQuery();
      searchEvents(searchQuery);
    } else if(newLocation?.city && newLocation.latitude && newLocation.longitude) {
      fetchNearbyEvents();
    } else {
      fetchEvents();
    }
    updateActiveFilters();
  },
  { deep: true }
);

watch(
  () => route.query,
  (newQuery, oldQuery) => {
    const newTag = newQuery.tag;
    const oldTag = oldQuery?.tag;

    console.log('Route watcher triggered:', { newTag, oldTag, newQuery, oldQuery });

    // Handle tag changes
    if (newTag && typeof newTag === 'string' && newTag !== oldTag) {
      console.log('Adding new tag:', newTag);
      routerStore.setTag(newTag);
      handleTagQuery(newTag);
    } else if (!newTag && oldTag) {
      console.log('Tag was removed, fetching regular events');
      routerStore.setTag(null);
      fetchEvents();
      updateActiveFilters();
    }
  },
  { deep: true }
);


</script>

<style lang="scss" scoped>
.moving-background {
  position: relative;
  background-image: url("/assets/images/hero.png");
  background-size: cover;
  background-position: center;
}

.shiny {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
  }
}
</style>
