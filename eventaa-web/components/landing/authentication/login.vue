<template>
  <button href="#" @click="openModal"
    class="font-medium text-gray-700 dark:text-zinc-100 hover:dark:text-zinc-50 hover:text-gray-800">
    Sign in
  </button>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" @close="closeModal" class="relative z-[9999999]">
      <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
        leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
        <div class="fixed inset-0 bg-black/25" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4 text-center">
          <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95">
            <DialogPanel
              class="w-full bg-gray-50 dark:bg-zinc-800 max-w-3xl transform overflow-hidden rounded-none text-left shadow-xl transition-all">
              <div>
                <div>
                  <div class="w-full grid grid-cols-1 md:grid-cols-5 gap-2">
                    <div
                      class="col-span-1 md:col-span-2 sm:flex hidden items-center justify-center relative moving-background h-40 md:h-auto">
                      <div class="flex flex-col items-center bg-opacity-25">
                        <img src="/icon.png" alt="eventa-malawi-logo"
                          class="w-16 h-16 md:w-20 md:h-20 object-cover rounded-full" />
                        <h3 class="w-full text-xl md:text-2xl font-semibold text-white dark:text-zinc-100 text-center">
                          EventaHub Malawi
                        </h3>
                      </div>
                    </div>
                    <div class="col-span-1 md:col-span-3 p-4 md:p-0">
                      <div class="justify-end flex items-end">
                        <button @click="closeModal" class="p-4 dark:text-zinc-100">
                          <Icon icon="gg:close" class="w-5 h-5" />
                        </button>
                      </div>
                      <div class="justify-center flex flex-col items-center space-y-1 mb-4">
                        <h3 class="text-xl md:text-2xl font-semibold text-center dark:text-zinc-50">
                          Welcome back🎉️
                        </h3>
                        <p class="text-gray-500 text-base text-center dark:text-zinc-200">
                          Login to continue browsing, organizing, managing
                          events and many more...
                        </p>
                      </div>
                      <div v-if="requires2FA">
                        <TwoFactorVerification :email="email" @verified="onTwoFactorVerified"
                          @cancel="requires2FA = false" />
                      </div>
                      <FormKit v-else id="loginForm" @submit="onSubmitLogin" type="form" submit-label="Update"
                        :actions="false">
                        <div class="w-full flex flex-col items-center space-y-2 px-2 md:px-5">
                          <FormKit type="email" v-model="email" placeholder="Enter email address" label="Email address"
                            prefixIcon="email" validation="required|email" />
                          <FormKit type="password" v-model="password" placeholder="Enter password" label="Password"
                            prefixIcon="password" validation="required|string" />
                          <div class="w-full flex items-center justify-between">
                            <FormKit type="checkbox" label="Remember me" :value="true" />
                            <NuxtLink to="/forgot-password" class="text-sky-500 hover:underline transition duration-150"
                              @click="isOpen = false">
                              Forgot Password
                            </NuxtLink>
                          </div>
                          <CoreSubmitButton text="Continue to login" :loading="loading" />
                          <div>
                            <p class="text-base dark:text-zinc-100">
                              Do not have an account?
                              <span class="hover:underline text-sky-500 transition duration-150 cursor-pointer" @click="openRegisterModal">
                                Register
                              </span>
                            </p>
                          </div>
                          <div class="w-full flex items-center justify-center flex-col space-y-2 pb-5">
                            <p class="text-base dark:text-zinc-100">OR</p>
                            <ClientOnly>
                              <GoogleLogin :callback="callback" :prompt="true" :auto-login="true"></GoogleLogin>
                            </ClientOnly>
                          </div>
                        </div>
                      </FormKit>
                    </div>
                  </div>
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
} from "@headlessui/vue";
import { useAuthStore } from "@/store/auth";
import { decodeCredential } from 'vue3-google-login'
import TwoFactorVerification from '@/components/auth/TwoFactorVerification.vue';

interface Props {
  onOpenRegister?: () => void;
}

const props = withDefaults(defineProps<Props>(), {
  onOpenRegister: () => {}
});

const httpClient = useHttpClient();
const authStore = useAuthStore();
const { setAuth } = authStore;
const email = ref<string>("");
const password = ref<string>("");
const loading = ref<boolean>(false);
const isOpen = ref<boolean>(false);
const requires2FA = ref<boolean>(false);
const { $toast, $router }: any = useNuxtApp();

const closeModal = (): void => {
  isOpen.value = false;
};
const openModal = (): void => {
  isOpen.value = true;
};

const openRegisterModal = (): void => {
  closeModal();
  props.onOpenRegister();
};

const errorHandlers = {
  getErrorMessages: (obj: any): string[] => {
    if (!obj) return [];

    return Object.values(obj).flatMap(value => {
      if (Array.isArray(value)) return value;
      if (typeof value === 'string') return [value];
      if (typeof value === 'object' && value !== null) return errorHandlers.getErrorMessages(value);
      return [];
    });
  },

  formatErrors: (messages: string[]): string =>
    messages.length > 0 ? messages.join('\n') : 'An unexpected error occurred',

  parseError: (error: any): string[] => {
    if (error.message?.message) return errorHandlers.getErrorMessages(error.message.message);
    if (error.message?.email) return Array.isArray(error.message.email) ? error.message.email : [error.message.email];
    return [];
  }
};

const handleError = (error: any, toast: any): void => {
  const messages = errorHandlers.parseError(error);
  toast.error(errorHandlers.formatErrors(messages));
};

const onSubmitLogin = async (): Promise<void> => {
  loading.value = true;
  try {
    // Initialize CSRF token before login
    await httpClient.initializeCsrf();

    const response = await httpClient.post<any>(ENDPOINTS.AUTH.LOGIN, {
      email: email.value,
      password: password.value,
    });

    if (response) {
      if (response.requires_2fa) {
        requires2FA.value = true;
        $toast.info('Please enter the verification code sent to your email');
      } else {
        setAuth(response);
        $toast.success(response.message);
        closeModal();
      }
    }
  } catch (error: any) {
    if (error.message?.email_verification_required) {
      localStorage.setItem('pending_verification_email', error.message.email);
      $toast.error(error.message.message);
      closeModal();
      $router.push('/email/verify/pending');
    } else {
      handleError(error, $toast);
    }
  } finally {
    loading.value = false;
  }
}

const onTwoFactorVerified = (): void => {
  requires2FA.value = false;
  $toast.success('Login successful');
  closeModal();
}

const callback = async (e: { credential: string; }): Promise<void> => {
  loading.value = true;
  const userData: any = decodeCredential(e.credential);
  const formData = new FormData();
  formData.append('email', userData.email);
  formData.append('username', userData.name);
  formData.append('email_verified', userData.email_verified);
  formData.append('sub', userData.sub);
  formData.append('picture', userData.picture);
  try {
    const response: any = await httpClient.post(ENDPOINTS.AUTH.GOOGLE, formData);
    if (response) {
      setAuth(response);
      $toast.success(response.message);
      setTimeout(() => {
        $router.go();
      }, 1000)
    }
  } catch (error) {
    handleError(error, $toast);
  } finally {
    loading.value = false;
  }
};

defineExpose({
  openModal,
  closeModal,
});
</script>

<style lang="scss" scoped>
.moving-background {
  background-image: url("../../../assets/images/hero.png");
  background-size: cover;
  background-position: center;
}
</style>
